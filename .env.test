# Test environment configuration for PITAS
PITAS_ENV=test
DEBUG=true

# Security
SECRET_KEY=test-secret-key-for-integration-testing-only

# Database Configuration
POSTGRES_SERVER=localhost
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_password
POSTGRES_DB=test_pitas
DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_pitas

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Neo4j Configuration (Phase 3)
NEO4J_URL=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=test_neo4j_password

# InfluxDB Configuration (Phase 3)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=test-influxdb-token
INFLUXDB_ORG=test-org
INFLUXDB_BUCKET=test-bucket

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=PITAS Test Environment
PROJECT_VERSION=0.7.0

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Logging
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_ENABLED=false

# Integration Layer (Phase 7)
OBSIDIAN_VAULT_PATH=/tmp/test-obsidian-vault
CMDB_TYPE=test
KNOWLEDGE_BASE_TYPE=test

# Testing flags
SKIP_DATABASE_INIT=true
SKIP_EXTERNAL_SERVICES=true
