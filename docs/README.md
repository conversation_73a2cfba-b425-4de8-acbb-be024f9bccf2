
# PITAS Documentation

This directory contains the comprehensive documentation for PITAS (Pentesting Team Management System), built with <PERSON><PERSON>nx and featuring extensive coverage of Phase 6 (Employee Retention and Career Development) functionality.

## 📚 Documentation Overview

The documentation covers both Phase 5 (Training Delivery and Competency Management) and Phase 6 (Employee Retention and Career Development) implementations, providing complete guides for users, developers, and administrators.

### Key Documentation Sections

- **🚀 Quick Start Guide** - Get PITAS running in 10 minutes
- **🎯 Career Development** - Individual Development Plans, goal tracking, and progression frameworks
- **🏆 Recognition System** - Peer nominations, rewards, and achievement tracking
- **💡 Wellness Monitoring** - Burnout prevention and work-life balance tracking
- **👥 Mentorship System** - Advanced mentor-mentee matching and session management
- **📊 Analytics & Reporting** - Retention metrics and predictive analytics
- **🔧 API Reference** - Complete REST API documentation
- **🗄️ Database Schema** - Comprehensive data model documentation
- **⚙️ Configuration** - Environment setup and customization options

## 🏗️ Building the Documentation

### Prerequisites

- Python 3.11+
- pip package manager

### Quick Build

```bash
# Install dependencies
cd docs
pip install -r requirements.txt

# Build HTML documentation
make html

# View documentation
open build/html/index.html
```

### Development with Live Reload

```bash
# Install sphinx-autobuild for live reload
pip install sphinx-autobuild

# Start live reload server
make livehtml

# Documentation will be available at http://localhost:8000
```

### Build All Formats

```bash
# Build HTML, PDF, and EPUB
make all

# Individual formats
make html    # HTML documentation
make pdf     # PDF documentation
make epub    # EPUB documentation
```

## 📁 Documentation Structure

```
docs/
├── source/                     # Source files
│   ├── index.rst              # Main documentation index
│   ├── installation.rst       # Installation guide
│   ├── configuration.rst      # Configuration reference
│   ├── quickstart.rst         # Quick start guide
│   │
│   ├── career/                 # Phase 6: Career Development
│   │   ├── overview.rst        # Career development overview
│   │   ├── progression_framework.rst  # Four-tier career framework
│   │   ├── development_plans.rst      # Individual Development Plans
│   │   ├── recognition_system.rst     # Recognition and rewards
│   │   ├── wellness_monitoring.rst    # Wellness and work-life balance
│   │   └── mentorship_matching.rst    # Advanced mentorship system
│   │
│   ├── training/               # Phase 5: Training & Competency
│   │   └── overview.rst        # Training system overview
│   │
│   ├── api/                    # API Documentation
│   │   ├── overview.rst        # API overview and authentication
│   │   └── career_endpoints.rst       # Career development endpoints
│   │
│   ├── database/               # Database Documentation
│   │   └── career_models.rst   # Career development data models
│   │
│   ├── services/               # Business Logic Documentation
│   │   └── career_services.rst # Career development services
│   │
│   ├── analytics/              # Analytics Documentation
│   │   └── retention_metrics.rst      # Employee retention analytics
│   │
│   ├── _static/                # Static assets
│   │   └── custom.css          # Custom styling
│   │
│   └── conf.py                 # Sphinx configuration
│
├── build/                      # Generated documentation
│   └── html/                   # HTML output
│
├── requirements.txt            # Documentation dependencies
├── Makefile                    # Build automation
└── README.md                   # This file
```

## 🎨 Documentation Features

### Advanced Sphinx Features

- **Responsive Design** - Mobile-friendly documentation with RTD theme
- **Cross-References** - Automatic linking between sections and APIs
- **Code Highlighting** - Syntax highlighting for multiple languages
- **Search Functionality** - Full-text search across all documentation
- **Custom Styling** - PITAS-branded color scheme and layout

### Interactive Elements

- **Copy-to-Clipboard** - One-click code copying
- **Collapsible Sections** - Organized content with expandable sections
- **Tabbed Content** - Multiple examples and configurations
- **Mermaid Diagrams** - Interactive flowcharts and diagrams

### API Documentation

- **OpenAPI Integration** - Automatic API documentation generation
- **Request/Response Examples** - Complete API usage examples
- **Authentication Guides** - JWT token management
- **Error Handling** - Comprehensive error response documentation

## 📖 Key Documentation Highlights

### Phase 6 Career Development

The documentation extensively covers Phase 6 functionality:

1. **Career Progression Framework**
   - Four-tier advancement structure (Entry → Intermediate → Senior → Expert)
   - Multiple career tracks (Technical Specialist, Team Leadership, Client Consulting, R&D)
   - Competency-based advancement criteria

2. **Individual Development Plans (IDPs)**
   - Goal setting and tracking
   - Activity management
   - Progress monitoring
   - Quarterly review cycles

3. **Recognition and Rewards System**
   - Peer nomination workflows
   - Automated point calculations
   - Comprehensive reward catalog
   - Achievement tracking

4. **Wellness Monitoring**
   - Burnout risk assessment
   - Work-life balance tracking
   - Automated alert systems
   - Intervention workflows

5. **Advanced Mentorship**
   - Intelligent mentor-mentee matching
   - Session tracking and management
   - Effectiveness measurement
   - Analytics and reporting

### Technical Documentation

- **Database Models** - Complete schema documentation with relationships
- **Service Layer** - Business logic and workflow documentation
- **API Endpoints** - RESTful API with examples and error handling
- **Analytics Engine** - Retention metrics and predictive modeling

## 🔧 Customization

### Themes and Styling

The documentation uses a custom PITAS theme based on the Sphinx RTD theme:

- **Brand Colors** - PITAS color palette throughout
- **Custom CSS** - Enhanced styling in `_static/custom.css`
- **Responsive Design** - Mobile-optimized layout
- **Dark Mode Support** - Coming in future versions

### Configuration

Key configuration options in `conf.py`:

```python
# Project information
project = 'PITAS - Pentesting Team Management System'
version = '0.6.0'

# Extensions
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.napoleon',
    'myst_parser',
    'sphinx_rtd_theme',
    # ... more extensions
]

# Theme options
html_theme_options = {
    'style_nav_header_background': '#2980B9',
    'collapse_navigation': True,
    'navigation_depth': 4,
    # ... more options
}
```

## 🚀 Deployment

### GitHub Pages

The documentation can be automatically deployed to GitHub Pages:

```bash
# Build and deploy
make deploy
```

### Read the Docs

For Read the Docs integration:

1. Connect your GitHub repository
2. Configure build settings to use `docs/` directory
3. Set Python version to 3.11+
4. Documentation will auto-build on commits

### Docker Deployment

```bash
# Build documentation in Docker
docker run --rm -v $(pwd):/docs sphinxdoc/sphinx:latest make -C /docs html

# Serve with nginx
docker run -d -p 8080:80 -v $(pwd)/build/html:/usr/share/nginx/html nginx
```

## 📊 Documentation Metrics

The current documentation includes:

- **16 main documentation files**
- **5 major sections** (Career, API, Database, Services, Analytics)
- **50+ code examples** with syntax highlighting
- **20+ diagrams and tables**
- **Comprehensive API coverage** for all Phase 6 endpoints

## 🤝 Contributing to Documentation

### Writing Guidelines

1. **Use Clear Headings** - Hierarchical structure with descriptive titles
2. **Include Examples** - Code examples for all concepts
3. **Cross-Reference** - Link related sections and APIs
4. **Keep Updated** - Maintain accuracy with code changes

### Adding New Documentation

1. Create new `.rst` files in appropriate directories
2. Add to table of contents in `index.rst`
3. Include cross-references to related sections
4. Test build locally before committing

### Style Guide

- Use reStructuredText (`.rst`) format
- Follow existing heading hierarchy
- Include code examples with proper syntax highlighting
- Add cross-references using `:doc:` directive
- Use tables for structured data
- Include diagrams where helpful

## 🔍 Troubleshooting

### Common Build Issues

**Missing Dependencies:**
```bash
pip install -r requirements.txt
```

**Build Warnings:**
- Check for missing cross-references
- Verify file paths in toctree directives
- Ensure proper reStructuredText syntax

**Styling Issues:**
- Clear build directory: `make clean`
- Rebuild: `make html`
- Check custom CSS in `_static/custom.css`

### Getting Help

- **Documentation Issues** - Create GitHub issue with `documentation` label
- **Build Problems** - Check Sphinx documentation at https://www.sphinx-doc.org/
- **Styling Questions** - Review RTD theme documentation

## 📝 License

This documentation is part of the PITAS project and is licensed under the MIT License. See the main project LICENSE file for details.

---

**Built with ❤️ using Sphinx and the Read the Docs theme**

