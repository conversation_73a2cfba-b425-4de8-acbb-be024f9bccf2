{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "pitas-dev-shell";
  
  buildInputs = with pkgs; [
    # Python and package management
    python311
    python311Packages.pip
    python311Packages.virtualenv
    python311Packages.pipx
    
    # Database tools
    postgresql_15
    redis
    
    # Development tools
    git
    gh                    # GitHub CLI
    pre-commit
    
    # Code quality and formatting
    ruff
    black
    mypy
    
    # Testing tools
    playwright-driver
    
    # Security tools
    bandit
    semgrep
    
    # Documentation
    sphinx
    
    # Container and orchestration
    docker
    docker-compose
    kubectl
    helm
    
    # Monitoring and observability
    prometheus
    grafana
    
    # API and HTTP tools
    curl
    httpie
    jq
    
    # Text processing
    yq-go
    
    # Infrastructure as Code
    terraform
    ansible
    
    # Cloud tools
    awscli2
    google-cloud-sdk
    azure-cli
    
    # Database clients
    postgresql
    redis
    
    # Graph database
    # Note: Neo4j not available in nixpkgs, use Docker

    # Time series database
    influxdb2-cli
    # Note: InfluxDB server runs in Docker
    
    # Message queue
    # Note: Kafka tools would need separate setup
    
    # Development utilities
    direnv
    tmux
    vim
    neovim
    
    # Network tools
    netcat
    nmap
    
    # File utilities
    tree
    fd
    ripgrep
    bat
    
    # Process monitoring
    htop
    
    # SSL/TLS tools
    openssl
    
    # JSON/YAML processing
    yq
    
    # Load testing
    wrk
    
    # Linting and static analysis
    shellcheck
    
    # Documentation generation
    pandoc
    
    # Version control
    gitflow
    
    # Secrets management
    sops
    age
    
    # Performance profiling
    valgrind
    
    # Backup tools
    restic
  ];
  
  shellHook = ''
    echo "🔒 PITAS Development Environment"
    echo "================================"
    echo "Python: $(python --version)"
    echo "PostgreSQL: $(postgres --version | head -n1)"
    echo "Redis: $(redis-server --version)"
    echo "Docker: $(docker --version)"
    echo "Kubectl: $(kubectl version --client --short 2>/dev/null || echo 'kubectl not configured')"
    echo ""
    echo "📋 Available commands:"
    echo "  make setup     - Set up development environment"
    echo "  make test      - Run tests"
    echo "  make lint      - Run linting"
    echo "  make format    - Format code"
    echo "  make security  - Run security checks"
    echo "  make docs      - Build documentation"
    echo "  make docker    - Build Docker images"
    echo "  make deploy    - Deploy to development"
    echo ""
    
    # Set up Python virtual environment if it doesn't exist
    if [ ! -d ".venv" ]; then
      echo "🐍 Creating Python virtual environment..."
      python -m venv .venv
    fi
    
    # Activate virtual environment
    source .venv/bin/activate
    
    # Install Python dependencies if requirements files exist
    if [ -f "requirements.txt" ]; then
      echo "📦 Installing Python dependencies..."
      pip install -r requirements.txt
    fi
    
    # Install development dependencies
    if [ -f "pyproject.toml" ]; then
      echo "🔧 Installing development dependencies..."
      pip install -e ".[dev,docs,security]"
    fi
    
    # Set up pre-commit hooks
    if [ -f ".pre-commit-config.yaml" ]; then
      echo "🪝 Setting up pre-commit hooks..."
      pre-commit install
    fi
    
    # Environment variables
    export PYTHONPATH="$PWD/src:$PYTHONPATH"
    export PITAS_ENV="development"
    export PITAS_DEBUG="true"
    
    # Database URLs for development
    export DATABASE_URL="postgresql://pitas:pitas_password@localhost:5432/pitas_db"
    export TEST_DATABASE_URL="postgresql://test:test@localhost:5432/test_pitas"
    export REDIS_URL="redis://localhost:6379/0"
    export NEO4J_URL="bolt://localhost:7687"
    export NEO4J_USER="neo4j"
    export NEO4J_PASSWORD="pitas_neo4j"
    export INFLUXDB_URL="http://localhost:8086"
    export INFLUXDB_TOKEN="pitas-dev-token"
    export INFLUXDB_ORG="pitas"
    export INFLUXDB_BUCKET="vulnerability-metrics"
    
    # Security
    export SECRET_KEY="dev-secret-key-change-in-production"
    
    # API Keys (set these in your local environment)
    # export NVD_API_KEY="your-nvd-api-key"
    # export MITRE_API_KEY="your-mitre-api-key"
    
    echo "✅ Development environment ready!"
    echo "💡 Tip: Copy .env.example to .env and customize your settings"
    echo ""
  '';
  
  # Environment variables
  PYTHONPATH = "./src";
  PITAS_ENV = "development";
  
  # Prevent pip from using system packages
  PIP_PREFIX = "$(pwd)/.venv";
  PYTHONUSERBASE = "$(pwd)/.venv";
  
  # PostgreSQL configuration
  PGDATA = "$(pwd)/.postgres";
  PGHOST = "localhost";
  PGPORT = "5432";
  PGUSER = "pitas";
  PGDATABASE = "pitas_db";
}
