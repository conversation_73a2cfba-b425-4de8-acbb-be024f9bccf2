fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0
sqlalchemy>=2.0.23
alembic>=1.13.0
asyncpg>=0.29.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
httpx>=0.25.2
structlog>=23.2.0
rich>=13.7.0
redis>=5.0.0
celery>=5.3.0
neo4j>=5.14.0
influxdb-client>=1.38.0
prometheus-client>=0.19.0
opentelemetry-api>=1.21.0
opentelemetry-sdk>=1.21.0
opentelemetry-instrumentation-fastapi>=0.42b0
opentelemetry-exporter-jaeger-thrift>=1.21.0
opentelemetry-exporter-prometheus>=0.55b0
slowapi>=0.1.9
psutil>=5.9.0
aiohttp>=3.9.0

[dev]
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
pytest-mock>=3.12.0
ruff>=0.1.6
mypy>=1.7.1
pre-commit>=3.5.0
black>=23.11.0
isort>=5.12.0
hypothesis>=6.88.0
mutmut>=2.4.3
playwright>=1.40.0

[docs]
sphinx>=7.2.6
sphinx-rtd-theme>=1.3.0
sphinx-autodoc-typehints>=1.25.2
myst-parser>=2.0.0

[security]
bandit>=1.7.5
safety>=2.3.5
semgrep>=1.45.0
