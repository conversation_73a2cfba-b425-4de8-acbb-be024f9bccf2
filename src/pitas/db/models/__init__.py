"""Database models package."""

# Import all models to ensure they are registered with SQLAlchemy
from .base import Base
from .user import User, User<PERSON><PERSON>, CareerTier, CareerTrack

# Phase 2: Team Resource Management models
from .pentester import PentesterProfile
from .project import Project
from .resource_allocation import ResourceAllocation
from .skill_matrix import SkillMatrix, SecurityDomain, CertificationTier
from .capacity_plan import CapacityPlan

# Phase 3: Vulnerability Assessment models
from .vulnerability import (
    Vulnerability,
    VulnerabilityStatus,
    SeverityLevel,
    VulnerabilitySource,
    VulnerabilityFinding,
    ThreatIntelligence,
    SecurityEvent
)

from .asset import (
    Asset,
    AssetVulnerability,
    AssetCriticality,
)

from .risk_assessment import (
    RiskAssessment,
    ThreatIntelligence,
    RemediationPlan,
    RiskLevel,
    ThreatActorType,
)

# Phase 4: Project Workflow and Remediation models
from .client import Client, ClientPortalUser, ClientDocument, ClientCommunication
from .remediation import (
    Remediation,
    RemediationEscalation,
    RemediationComment,
    RemediationAttachment
)
from .compliance import (
    ComplianceMapping,
    AuditTrail,
    ComplianceReport,
    ComplianceEvidence
)

# Phase 5: Training models
from .training import (
    CompetencyFramework,
    Competency,
    SkillAssessment,
    TrainingCourse,
    LearningPath,
    TrainingEnrollment,
    Certification,
    CertificationAchievement,
    CTFChallenge,
    CTFSubmission,
    MentorshipPair,
    MentorshipSession,
    CompetencyLevel,
    TrainingStatus,
    CertificationStatus,
)

# Phase 6: Career development models
from .career import (
    IndividualDevelopmentPlan,
    DevelopmentGoal,
    DevelopmentActivity,
    IDPStatus,
    GoalStatus,
    GoalPriority,
    ActivityType,
    ActivityStatus,
)
from .recognition import (
    Recognition,
    PeerNomination,
    NominationVote,
    Reward,
    RecognitionType,
    RecognitionStatus,
    AchievementCategory,
    RewardType,
    RewardStatus,
)
from .wellness import (
    WellnessCheck,
    WellnessAlert,
    WorkSchedule,
    WellnessResource,
    WellnessMetricType,
    AlertSeverity,
    AlertStatus,
    WorkScheduleType,
)
from .mentorship import (
    Mentorship,
    MentorshipSession,
    MentorProfile,
    MentorshipRequest,
    MentorshipType,
    MentorshipStatus,
    SessionType,
    SessionStatus,
)

__all__ = [
    # Base
    "Base",
    # User models
    "User",
    "UserRole",
    "CareerTier",
    "CareerTrack",
    # Phase 2: Team Resource Management models
    "PentesterProfile",
    "Project",
    "ResourceAllocation",
    "SkillMatrix",
    "SecurityDomain",
    "CertificationTier",
    "CapacityPlan",
    # Phase 3: Vulnerability Assessment models
    "Vulnerability",
    "VulnerabilityStatus",
    "SeverityLevel",
    "VulnerabilitySource",
    "VulnerabilityFinding",
    "ThreatIntelligence",
    "SecurityEvent",
    "Asset",
    "AssetVulnerability",
    "AssetCriticality",
    # Phase 3: Risk assessment models
    "RiskAssessment",
    "ThreatIntelligence",
    "RemediationPlan",
    "RiskLevel",
    "ThreatActorType",
    # Phase 4: Project Workflow and Remediation models
    "Client",
    "ClientPortalUser",
    "ClientDocument",
    "ClientCommunication",
    "Remediation",
    "RemediationEscalation",
    "RemediationComment",
    "RemediationAttachment",
    "ComplianceMapping",
    "AuditTrail",
    "ComplianceReport",
    "ComplianceEvidence",
    # Phase 5: Training models
    "CompetencyFramework",
    "Competency",
    "SkillAssessment",
    "TrainingCourse",
    "LearningPath",
    "TrainingEnrollment",
    "Certification",
    "CertificationAchievement",
    "CTFChallenge",
    "CTFSubmission",
    "MentorshipPair",
    "MentorshipSession",
    "CompetencyLevel",
    "TrainingStatus",
    "CertificationStatus",
    # Phase 6: Career development models
    "IndividualDevelopmentPlan",
    "DevelopmentGoal",
    "DevelopmentActivity",
    "IDPStatus",
    "GoalStatus",
    "GoalPriority",
    "ActivityType",
    "ActivityStatus",
    # Recognition models
    "Recognition",
    "PeerNomination",
    "NominationVote",
    "Reward",
    "RecognitionType",
    "RecognitionStatus",
    "AchievementCategory",
    "RewardType",
    "RewardStatus",
    # Wellness models
    "WellnessCheck",
    "WellnessAlert",
    "WorkSchedule",
    "WellnessResource",
    "WellnessMetricType",
    "AlertSeverity",
    "AlertStatus",
    "WorkScheduleType",
    # Mentorship models
    "Mentorship",
    "MentorshipSession",
    "MentorProfile",
    "MentorshipRequest",
    "MentorshipType",
    "MentorshipStatus",
    "SessionType",
    "SessionStatus",
]