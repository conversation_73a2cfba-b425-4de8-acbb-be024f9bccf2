"""Vulnerability tracking models for Phase 3."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional, List
from uuid import UUID

from sqlalchemy import (
    String, Text, DateTime, Numeric, Integer, Boolean,
    ForeignKey, JSON, Index, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class VulnerabilitySeverity(str, Enum):
    """CVSS severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
<<<<<<< HEAD
    INFO = "info"


class VulnerabilitySource(str, Enum):
    """Sources of vulnerability data."""
    NESSUS = "nessus"
    QUALYS = "qualys"
    RAPID7 = "rapid7"
    OPENVAS = "openvas"
    MANUAL = "manual"
    THREAT_INTEL = "threat_intel"
    PENETRATION_TEST = "penetration_test"


class Vulnerability(Base):
    """Vulnerability model for security tool integration."""
    
    __tablename__ = "vulnerabilities"
    
    # Basic information
    title: Mapped[str] = mapped_column(String(500), nullable=False, index=True)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    status: Mapped[VulnerabilityStatus] = mapped_column(String(20), default=VulnerabilityStatus.OPEN)
    
    # Identifiers
    cve_id: Mapped[Optional[str]] = mapped_column(String(20), index=True)
    cwe_id: Mapped[Optional[str]] = mapped_column(String(20), index=True)
    plugin_id: Mapped[Optional[str]] = mapped_column(String(50))  # Scanner plugin ID
    
    # Severity and scoring
    severity: Mapped[SeverityLevel] = mapped_column(String(20), nullable=False, index=True)
    cvss_v2_score: Mapped[Optional[float]] = mapped_column(Float)
    cvss_v3_score: Mapped[Optional[float]] = mapped_column(Float)
    cvss_v4_score: Mapped[Optional[float]] = mapped_column(Float)
    cvss_vector: Mapped[Optional[str]] = mapped_column(String(200))
    
    # Risk assessment
    risk_score: Mapped[Optional[float]] = mapped_column(Float)
    exploitability_score: Mapped[Optional[float]] = mapped_column(Float)
    business_impact_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Source information
    source: Mapped[VulnerabilitySource] = mapped_column(String(50), nullable=False)
    source_id: Mapped[Optional[str]] = mapped_column(String(255))  # ID in source system
    scanner_name: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Discovery information
    first_discovered: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_seen: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Technical details
    affected_software: Mapped[Optional[str]] = mapped_column(String(500))
    affected_versions: Mapped[Optional[str]] = mapped_column(String(500))
    port: Mapped[Optional[int]] = mapped_column(Integer)
    protocol: Mapped[Optional[str]] = mapped_column(String(20))
    service: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Evidence and proof
    evidence: Mapped[Optional[str]] = mapped_column(Text)
    proof_of_concept: Mapped[Optional[str]] = mapped_column(Text)
    
    # Remediation
    solution: Mapped[Optional[str]] = mapped_column(Text)
    workaround: Mapped[Optional[str]] = mapped_column(Text)
    remediation_effort: Mapped[Optional[str]] = mapped_column(String(20))  # low, medium, high
    
    # References
    references: Mapped[Optional[list[str]]] = mapped_column(JSON)
    external_references: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # MITRE ATT&CK mapping
    mitre_techniques: Mapped[Optional[list[str]]] = mapped_column(JSON)
    mitre_tactics: Mapped[Optional[list[str]]] = mapped_column(JSON)
    
    # Metadata
    tags: Mapped[Optional[list[str]]] = mapped_column(JSON)
    custom_fields: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Relationships
    findings: Mapped[list["VulnerabilityFinding"]] = relationship(
        "VulnerabilityFinding",
        back_populates="vulnerability",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Vulnerability(title='{self.title}', cve='{self.cve_id}', severity='{self.severity}')>"


class VulnerabilityFinding(Base):
    """Specific instances of vulnerabilities found on assets."""
    
    __tablename__ = "vulnerability_findings"
    
    vulnerability_id: Mapped[UUID] = mapped_column(
        ForeignKey("vulnerabilities.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    asset_id: Mapped[UUID] = mapped_column(
        ForeignKey("assets.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Finding-specific information
    status: Mapped[VulnerabilityStatus] = mapped_column(String(20), default=VulnerabilityStatus.OPEN)
    
    # Discovery details
    discovered_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_verified: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Scanner information
    scan_id: Mapped[Optional[str]] = mapped_column(String(255))
    scanner_finding_id: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Asset-specific details
    asset_specific_evidence: Mapped[Optional[str]] = mapped_column(Text)
    asset_specific_impact: Mapped[Optional[str]] = mapped_column(Text)
    
    # Remediation tracking
    assigned_to: Mapped[Optional[str]] = mapped_column(String(255))
    remediation_status: Mapped[Optional[str]] = mapped_column(String(50))
    remediation_notes: Mapped[Optional[str]] = mapped_column(Text)
    remediation_deadline: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    resolved_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Verification
    verified_by: Mapped[Optional[str]] = mapped_column(String(255))
    verification_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # Risk context
    business_context: Mapped[Optional[str]] = mapped_column(Text)
    environmental_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Relationships
    vulnerability: Mapped["Vulnerability"] = relationship("Vulnerability", back_populates="findings")
    
    def __repr__(self) -> str:
        return f"<VulnerabilityFinding(vuln_id='{self.vulnerability_id}', asset_id='{self.asset_id}')>"


class ThreatIntelligence(Base):
    """Threat intelligence data for vulnerability enrichment."""
    
    __tablename__ = "threat_intelligence"
    
    # Threat information
    threat_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    threat_type: Mapped[str] = mapped_column(String(100), nullable=False)
    threat_name: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # Associated vulnerabilities
    cve_ids: Mapped[Optional[list[str]]] = mapped_column(JSON)
    
    # Threat details
    description: Mapped[str] = mapped_column(Text, nullable=False)
    severity: Mapped[SeverityLevel] = mapped_column(String(20), nullable=False)
    confidence: Mapped[float] = mapped_column(Float, nullable=False)  # 0.0 to 1.0
    
    # Indicators of Compromise (IoCs)
    iocs: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # MITRE ATT&CK mapping
    mitre_techniques: Mapped[Optional[list[str]]] = mapped_column(JSON)
    mitre_tactics: Mapped[Optional[list[str]]] = mapped_column(JSON)
    
    # Temporal information
    first_seen: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_seen: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Source information
    source: Mapped[str] = mapped_column(String(100), nullable=False)
    source_confidence: Mapped[Optional[float]] = mapped_column(Float)
    
    # Intelligence metadata
    tags: Mapped[Optional[list[str]]] = mapped_column(JSON)
    references: Mapped[Optional[list[str]]] = mapped_column(JSON)
    
    # Enrichment data
    enrichment_data: Mapped[Optional[dict]] = mapped_column(JSON)
    
    def __repr__(self) -> str:
        return f"<ThreatIntelligence(name='{self.threat_name}', type='{self.threat_type}')>"


class SecurityEvent(Base):
    """Security events from SIEM systems."""
    
    __tablename__ = "security_events"
    
    # Event identification
    event_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    source_system: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # Event details
    event_type: Mapped[str] = mapped_column(String(100), nullable=False)
    event_name: Mapped[str] = mapped_column(String(500), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Severity and classification
    severity: Mapped[SeverityLevel] = mapped_column(String(20), nullable=False)
    category: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # Temporal information
    event_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    ingested_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    
    # Source information
    source_ip: Mapped[Optional[str]] = mapped_column(String(45))
    source_hostname: Mapped[Optional[str]] = mapped_column(String(255))
    destination_ip: Mapped[Optional[str]] = mapped_column(String(45))
    destination_hostname: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Event data
    raw_event: Mapped[Optional[dict]] = mapped_column(JSON)
    parsed_fields: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Correlation
    correlation_id: Mapped[Optional[str]] = mapped_column(String(255), index=True)
    related_vulnerabilities: Mapped[Optional[list[str]]] = mapped_column(JSON)
    
    # MITRE ATT&CK mapping
    mitre_techniques: Mapped[Optional[list[str]]] = mapped_column(JSON)
    mitre_tactics: Mapped[Optional[list[str]]] = mapped_column(JSON)
    
    def __repr__(self) -> str:
        return f"<SecurityEvent(event_id='{self.event_id}', type='{self.event_type}')>"
=======
    INFORMATIONAL = "informational"


class VulnerabilityStatus(str, Enum):
    """Vulnerability lifecycle status."""
    DISCOVERED = "discovered"
    CONFIRMED = "confirmed"
    TRIAGED = "triaged"
    IN_PROGRESS = "in_progress"
    REMEDIATED = "remediated"
    VERIFIED = "verified"
    CLOSED = "closed"
    FALSE_POSITIVE = "false_positive"
    ACCEPTED_RISK = "accepted_risk"


class AssetCriticality(str, Enum):
    """Business criticality levels for assets."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class Vulnerability(Base):
    """Core vulnerability tracking model."""
    
    __tablename__ = "vulnerabilities"
    __table_args__ = (
        Index("idx_vulnerability_cve", "cve_id"),
        Index("idx_vulnerability_severity", "severity"),
        Index("idx_vulnerability_status", "status"),
        Index("idx_vulnerability_discovery_date", "discovery_date"),
        Index("idx_vulnerability_cvss_score", "cvss_score"),
        CheckConstraint("cvss_score >= 0.0 AND cvss_score <= 10.0", name="cvss_score_range"),
    )

    # Core identification
    cve_id: Mapped[Optional[str]] = mapped_column(
        String(20), unique=True, index=True,
        doc="CVE identifier (e.g., CVE-2023-1234)"
    )
    
    title: Mapped[str] = mapped_column(
        String(500), nullable=False,
        doc="Vulnerability title or summary"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Detailed vulnerability description"
    )

    # CVSS scoring
    cvss_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="CVSS base score (0.0-10.0)"
    )
    
    cvss_vector: Mapped[Optional[str]] = mapped_column(
        String(200),
        doc="CVSS vector string"
    )
    
    severity: Mapped[VulnerabilitySeverity] = mapped_column(
        String(20), nullable=False, default=VulnerabilitySeverity.MEDIUM,
        doc="Vulnerability severity level"
    )

    # Lifecycle tracking
    status: Mapped[VulnerabilityStatus] = mapped_column(
        String(20), nullable=False, default=VulnerabilityStatus.DISCOVERED,
        doc="Current vulnerability status"
    )
    
    discovery_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False,
        doc="When the vulnerability was discovered"
    )
    
    remediation_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="When the vulnerability was remediated"
    )
    
    verification_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="When the remediation was verified"
    )

    # Risk assessment
    business_impact_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Business impact score (0.0-10.0)"
    )
    
    exploitability_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Exploitability score (0.0-10.0)"
    )
    
    true_risk_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="TruRisk methodology score"
    )

    # Metadata
    source: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Vulnerability source (scanner, manual, etc.)"
    )
    
    tags: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Vulnerability tags for categorization"
    )
    
    extra_data: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional vulnerability metadata"
    )

    # Relationships
    asset_vulnerabilities: Mapped[List["AssetVulnerability"]] = relationship(
        "AssetVulnerability", back_populates="vulnerability", cascade="all, delete-orphan"
    )


class Asset(Base):
    """Asset model for vulnerability correlation."""
    
    __tablename__ = "assets"
    __table_args__ = (
        Index("idx_asset_name", "name"),
        Index("idx_asset_criticality", "business_criticality"),
        Index("idx_asset_ip_address", "ip_address"),
    )

    name: Mapped[str] = mapped_column(
        String(255), nullable=False, unique=True,
        doc="Asset name or identifier"
    )
    
    asset_type: Mapped[str] = mapped_column(
        String(50), nullable=False,
        doc="Type of asset (server, application, network, etc.)"
    )
    
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(45),
        doc="IP address (IPv4 or IPv6)"
    )
    
    hostname: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Hostname or FQDN"
    )
    
    business_criticality: Mapped[AssetCriticality] = mapped_column(
        String(20), nullable=False, default=AssetCriticality.MEDIUM,
        doc="Business criticality level"
    )
    
    business_processes: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Business processes supported by this asset"
    )
    
    compliance_requirements: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Compliance frameworks applicable to this asset"
    )
    
    extra_data: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional asset metadata"
    )

    # Relationships
    asset_vulnerabilities: Mapped[List["AssetVulnerability"]] = relationship(
        "AssetVulnerability", back_populates="asset", cascade="all, delete-orphan"
    )


class AssetVulnerability(Base):
    """Association between assets and vulnerabilities."""
    
    __tablename__ = "asset_vulnerabilities"
    __table_args__ = (
        Index("idx_asset_vuln_asset", "asset_id"),
        Index("idx_asset_vuln_vulnerability", "vulnerability_id"),
        Index("idx_asset_vuln_impact", "impact_level"),
    )

    asset_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False
    )
    
    vulnerability_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("vulnerabilities.id"), nullable=False
    )
    
    impact_level: Mapped[str] = mapped_column(
        String(20), nullable=False,
        doc="Impact level of this vulnerability on this specific asset"
    )
    
    exploitability_likelihood: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Likelihood of exploitation for this asset (0.0-10.0)"
    )
    
    remediation_priority: Mapped[Optional[int]] = mapped_column(
        Integer,
        doc="Remediation priority ranking"
    )
    
    remediation_effort: Mapped[Optional[str]] = mapped_column(
        String(20),
        doc="Estimated remediation effort (low, medium, high)"
    )

    # Relationships
    asset: Mapped["Asset"] = relationship("Asset", back_populates="asset_vulnerabilities")
    vulnerability: Mapped["Vulnerability"] = relationship("Vulnerability", back_populates="asset_vulnerabilities")


class VulnerabilityMetric(Base):
    """Time-series metrics for vulnerability tracking."""
    
    __tablename__ = "vulnerability_metrics"
    __table_args__ = (
        Index("idx_vuln_metric_timestamp", "timestamp"),
        Index("idx_vuln_metric_type", "metric_type"),
        Index("idx_vuln_metric_asset", "asset_id"),
    )

    metric_type: Mapped[str] = mapped_column(
        String(50), nullable=False,
        doc="Type of metric (discovery_rate, remediation_time, etc.)"
    )
    
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False,
        doc="Metric timestamp"
    )
    
    value: Mapped[Decimal] = mapped_column(
        Numeric(10, 2), nullable=False,
        doc="Metric value"
    )
    
    asset_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("assets.id"),
        doc="Associated asset (if metric is asset-specific)"
    )
    
    vulnerability_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("vulnerabilities.id"),
        doc="Associated vulnerability (if metric is vulnerability-specific)"
    )
    
    extra_data: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional metric metadata"
    )
>>>>>>> integrate-phase8-with-phase11
